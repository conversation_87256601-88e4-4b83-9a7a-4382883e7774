import { Component, OnInit } from '@angular/core';
import { Table } from 'primeng/table';
import { CustomerService } from '../customer.service';
import { ActivatedRoute, Router } from '@angular/router';

interface expandedRows {
  [key: string]: boolean;
}

@Component({
  selector: 'app-customer-details',
  templateUrl: './customer-details.component.html',
  styleUrl: './customer-details.component.scss',
})
export class CustomerDetailsComponent implements OnInit {
  tabs: { title: string; value: string }[] = [
    { title: 'General', value: 'GENERAL' },
    { title: 'Companies', value: 'COMPANIES' },
    { title: 'Partner Function', value: 'PARTNER_FUNCTION' },
    { title: 'Sales Area', value: 'SALES_AREA' },
    { title: 'Texts', value: 'TEXTS' },
  ];
  public customerDetails: any = null;
  public filteredCustomers: any[] = [];
  public selectedCustomer: any;
  public isExpanded: boolean = false;
  public expandedRows: expandedRows = {};
  public bp_id: string = '';

  constructor(
    private customerService: CustomerService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.bp_id = this.route.snapshot.paramMap.get('id') || '';
    this.route.paramMap.subscribe((params) => {
      const customerId = params.get('id');
      if (customerId) {
        this.loadCustomerData(customerId);
      }
    });
  }

  private loadCustomerData(customerId: string): void {
    this.customerService.getCustomerByID(customerId).subscribe({
      next: (response: any) => {
        this.customerDetails = response?.data[0] || null;
        console.log('API Data:', this.customerDetails);
      },
      error: (error) => {
        console.error('Error fetching data:', error);
      },
    });
  }

  searchCustomers(event: any): void {
    const query = event.query.toLowerCase();
    this.customerService.getCustomerByIDName(query).subscribe((response: any) => {
      this.filteredCustomers = response.data.map((customer: any) => ({
        id: customer.customer_id,
        name: customer.customer_name,
        searchword:customer.customer_id + " - " +  customer.customer_name,
      }));
    });
  }

  onCustomerSelect(customer: any): void {
    const customerId = customer.value.id;
    if (customerId) {
      this.router.navigate(['/backoffice/customer/', customerId]);
    } else {
      console.error('Customer ID is undefined or null');
    }
  }

  goToBack() {
    this.router.navigate(['/backoffice/customer']);
  }
}
