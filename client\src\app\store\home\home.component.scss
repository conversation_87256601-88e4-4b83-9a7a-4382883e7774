.surface-card {
    .overview-banner {
        img {
            object-fit: cover;
        }

        &:before {
            position: absolute;
            content: "";
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(0deg, #00000070, transparent);
            mix-blend-mode: multiply;
        }
    }
}

.home-box-list {
    max-width: 1600px;
    .home-box {
        transition: all 0.3s ease-in-out;

        &:hover {
            transform: scale(1.1);
        }
    }
}
.text-md {
    font-size: 1.1rem !important;
}