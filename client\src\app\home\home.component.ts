import { Component, Renderer2 } from '@angular/core';
import { PrimeNGConfig } from 'primeng/api';
import { ContentService } from '../core/services/content-vendor.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent {

  content!: any;
  commonContent!: any;
  bannerData: any = null;
  servicesData: any[] = [];

  constructor(
    private primengConfig: PrimeNGConfig,
    private renderer: Renderer2,
    private route: ActivatedRoute,
    private CMSservice: ContentService
  ) { }

  ngOnInit(): void {
    this.commonContent = this.route.snapshot.data['commonContent'];
    console.log('Common Content:', this.commonContent);

    this.content = this.route.snapshot.data['content'];
    console.log('Home Content:', this.content);

    // Extract banner
    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, "public-sector.banner");
    if (bannerComponent?.length) {
      this.bannerData = bannerComponent[0];
    }

    // Extract services
    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, "public-sector.services");
    if (servicesComponents?.length) {
      this.servicesData = servicesComponents;
    }

    // Inject theme 
    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';
    const link = this.renderer.createElement('link');
    this.renderer.setAttribute(link, 'id', 'theme-link');
    this.renderer.setAttribute(link, 'rel', 'stylesheet');
    this.renderer.setAttribute(link, 'type', 'text/css');
    this.renderer.setAttribute(link, 'href', href);

    // Append the link tag to the head of the document
    this.renderer.appendChild(document.head, link);

    this.primengConfig.ripple = true; //enables core ripple functionality
  }

  ngOnDestroy(): void {
    // Find and remove the link tag when the component is destroyed
    const link = document.getElementById('theme-link');
    if (link) {
      link.remove();
    }
  }

}
