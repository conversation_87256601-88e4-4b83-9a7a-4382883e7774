import { OnInit } from '@angular/core';
import { Component } from '@angular/core';
import { MenuService } from './app.menu.service';

@Component({
  selector: 'app-menu',
  templateUrl: './app.menu.component.html',
})
export class AppMenuComponent implements OnInit {
  model: any[] = [];

  ngOnInit() {
    this.model = [
      {
        // label: 'Apps',
        icon: 'pi pi-th-large',
        items: [
          {
            label: 'Home',
            icon: 'home',
            routerLink: ['/store'],
            command: (event: any) => this.setActiveMenu(event.item.label),
          }
        ],
      },
    ];
  }

  constructor(private menuService: MenuService) { }

  setActiveMenu(menuName: string): void {
    this.menuService.setActiveMenu(menuName);
  }
}
