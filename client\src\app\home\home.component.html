<app-header [commonContent]="commonContent"></app-header>

<!--BANNER SEC-->
<section class="banner-sec relative">
    <div class="banner-body h-full flex align-items-center">
        <div class="banner-box relative flex-1 p-8"
             [style.background-image]="bannerData?.Image?.url ? 'url(' + bannerData.Image.url + ')' : null"
             [style.background-size]="'cover'"
             [style.background-position]="'center'"></div>
        <div class="banner-box relative flex-1 bg-white p-8 flex flex-column justify-content-center">
            <h4 class="mb-4 text-yellow-400">{{ bannerData?.Annotation || 'Far away from the every day!' }}</h4>
            <h1 class="m-0 mb-5 text-8xl line-height-1 font-extrabold text-color">{{ bannerData?.Title || 'Community of endless beauty & Calm' }}</h1>
            <p class="m-0 text-lg font-medium">{{ bannerData?.Description || 'Drawn by clean air and mythical light, visitors come to experience traditions, fine art, great cuisine and natural beauty of the landscape.' }}</p>
        </div>
    </div>
</section>
<!--BANNER SEC-->

<!--SERVICES SEC-->
<section class="services-sec w-full">
    <div class="services-body relative max-w-1200 w-full mx-auto px-4 flex">
        <div class="services-box p-5 flex-1 flex flex-column gap-2"
             *ngFor="let service of servicesData; let i = index"
             [ngClass]="'s-box-' + (i + 1)">
            <img [src]="service.Icon?.url" class="h-fit w-5rem" [alt]="service.Title" />
            <h4 class="font-bold text-white">{{ service.Title }}</h4>
            <div [innerHTML]="service.Description" class="flex-grow-1"></div>
            <button type="button"
                class="p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none">
                {{ service.Button_Title }} <span class="material-symbols-rounded">arrow_right_alt</span>
            </button>
        </div>
    </div>
</section>
<!--SERVICES SEC-->

<!--ABOUT SEC-->
<section class="about-sec relative">
    <div class="about-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap">
        <div class="about-box pr-8 w-6 secondary-bg-color">
            <img src="/assets/layout/images/director-img.jpg" alt="" class="w-full" />
        </div>
        <div class="about-box p-8 pr-0 w-6 secondary-bg-color flex flex-column justify-content-start">
            <h4 class="mb-4 text-yellow-400">Far away from the every day!</h4>
            <h2 class="line m-0 mb-6 relative pb-5 text-6xl line-height-2 font-bold text-white">A Message From the
                Director
            </h2>
            <p class="m-0 text-lg text-white flex-1">Old Town of Riverside is a special place with limitless potential,
                where everyone deserves equal access
                to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every
                neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>

            <button type="button"
                class="px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none">
                Learn More <span class="material-symbols-rounded">arrow_right_alt</span>
            </button>
        </div>
    </div>
</section>
<!--ABOUT SEC-->

<!--CITY MEMBERS SEC-->
<section class="city-members-sec relative">
    <div class="city-members-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap">
        <div class="city-members-left pt-8 pr-7 pb-8 pl-0 relative flex bg-orange-50">
            <div class="cm-time-box">
                <img src="/assets/layout/images/time-screen.png" alt="" class="w-full" />
            </div>
            <div class="cm-info">
                <h4 class="mb-4 text-yellow-400">Our city in numbers</h4>
                <h2 class="line m-0 mb-6 relative pb-5 text-4xl line-height-2 font-bold text-color">Programs to help
                    launch,
                    grow and expand any business</h2>
                <p class="m-0 text-lg text-color flex-1">Old Town of Riverside is a special place with limitless
                    potential, where everyone deserves equal access
                    to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in
                    every
                    neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>
            </div>
        </div>
        <div class="city-members-right p-7 pr-0 relative flex flex-column gap-8">
            <div class="cm-right-box">
                <h3 class="flex align-items-start text-8xl font-extrabold line-height-1">41 <sup
                        class="relative text-4xl font-bold inline-block">k</sup></h3>
                <h5 class="uppercase">Highly-educated creative workforce</h5>
                <p class="text-color-secondary">A vibrant destination where innovations in design and technology are
                    born. A place for you.</p>
            </div>
            <div class="cm-right-box">
                <h3 class=" flex align-items-start text-8xl font-extrabold line-height-1">8 <sup
                        class="relative text-4xl font-bold inline-block">th</sup></h3>
                <h5 class="uppercase">Highest Per Capita Income in Virginia</h5>
                <p class="text-color-secondary">A vibrant destination where innovations in design and technology are
                    born. A place for you.</p>
            </div>
        </div>
    </div>
</section>
<!--CITY MEMBERS SEC-->

<!--QUICK ACCESS SEC-->
<section class="quick-access-sec relative">
    <div class="quick-access-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap">
        <div class="quick-access-left relative flex flex-column pt-8 pb-8">
            <h2 class="mb-4 flex align-items-start text-6xl font-extrabold line-height-1">Quick Access</h2>
            <h4 class="line-2 inline-flex w-fit m-0 mb-6 relative pb-5 text-xl line-height-2 font-bold text-yellow-400">
                What can the City help you with today?</h4>

            <div class="quick-access-list d-grid w-full">
                <div
                    class="quick-access-box p-4 w-full flex flex-column border-bottom-none border-1 border-solid surface-border">
                    <img src="/assets/layout/images/001-toll.png" alt="" class="mb-4 w-4rem" />
                    <h6 class="m-0 mb-4 uppercase flex-1">Road construction</h6>
                    <p class="text-sm">Find current information about construction projects and other city-wide road
                        work impacting you.</p>
                </div>
                <div
                    class="quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border">
                    <img src="/assets/layout/images/006-virus.png" alt="" class="mb-4 w-4rem" />
                    <h6 class="m-0 mb-4 uppercase flex-1">COVID-19 updates</h6>
                    <p class="text-sm">Find current information about construction projects and other city-wide road
                        work impacting you.</p>
                </div>
                <div
                    class="quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border">
                    <img src="/assets/layout/images/003-trash-can.png" alt="" class="mb-4 w-4rem" />
                    <h6 class="m-0 mb-4 uppercase flex-1">Garbage pickup</h6>
                    <p class="text-sm">Find current information about construction projects and other city-wide road
                        work impacting you.</p>
                </div>
                <div class="quick-access-box p-4 w-full flex flex-column border-1 border-solid surface-border">
                    <img src="/assets/layout/images/004-parking.png" alt="" class="mb-4 w-4rem" />
                    <h6 class="m-0 mb-4 uppercase flex-1">Parking</h6>
                    <p class="text-sm">Find current information about construction projects and other city-wide road
                        work impacting you.</p>
                </div>
                <div
                    class="quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border">
                    <img src="/assets/layout/images/005-lecture.png" alt="" class="mb-4 w-4rem" />
                    <h6 class="m-0 mb-4 uppercase flex-1">Council meetings</h6>
                    <p class="text-sm">Find current information about construction projects and other city-wide road
                        work impacting you.</p>
                </div>
                <div
                    class="quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border">
                    <img src="/assets/layout/images/008-basketball.png" alt="" class="mb-4 w-4rem" />
                    <h6 class="m-0 mb-4 uppercase flex-1">Recreation and sport programs</h6>
                    <p class="text-sm">Find current information about construction projects and other city-wide road
                        work impacting you.</p>
                </div>
            </div>
        </div>
        <div class="quick-access-right"></div>
    </div>
</section>
<!--QUICK ACCESS SEC-->

<!--NEWS SEC-->
<section class="news-sec relative pt-7 pb-8 secondary-bg-color">
    <div class="news-body relative max-w-1200 w-full mx-auto px-4">
        <div class="news-title">
            <h5 class="line m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400">Find out what’s going on & stay up
                to date.</h5>
            <h2 class="m-0 mb-6 relative text-6xl line-height-2 font-bold text-white">Latest News</h2>
        </div>
        <div class="news-list d-grid w-full gap-6">
            <div class="news-box w-full flex flex-column">
                <div class="new-img flex align-items-center justify-content-center overflow-hidden">
                    <img src="/assets/layout/images/new-img-1.jpg" alt="" class="w-full h-full object-fit-cover">
                </div>
                <h3 class="text-white text-xl flex-1">Proposed Downtown District Ordinance</h3>
                <div class="mb-3 flex gap-3 align-items-center">
                    <div class="text flex align-items-center font-medium text-sm gap-1 text-white">
                        <span class="material-symbols-rounded text-xl">calendar_month</span> September 16, 2018
                    </div>
                    <div class="text flex align-items-center font-medium text-sm gap-1 text-white">
                        <span class="material-symbols-rounded text-xl">visibility</span> 240
                    </div>
                </div>
                <p class="mb-5 text-sm text-white">The goal of the proposed ordinance is to create safe venues for
                    customers
                    and to discourage illegal activities. The governing officials of...</p>

                <button type="button"
                    class="px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none">
                    Learn More <span class="material-symbols-rounded">arrow_right_alt</span>
                </button>
            </div>
            <div class="news-box w-full flex flex-column">
                <div class="new-img flex align-items-center justify-content-center overflow-hidden">
                    <img src="/assets/layout/images/new-img-1.jpg" alt="" class="w-full h-full object-fit-cover">
                </div>
                <h3 class="text-white text-xl flex-1">Annual Water Quality Report (Gallery Post)</h3>
                <div class="mb-3 flex gap-3 align-items-center">
                    <div class="text flex align-items-center font-medium text-sm gap-1 text-white">
                        <span class="material-symbols-rounded text-xl">calendar_month</span> September 16, 2018
                    </div>
                    <div class="text flex align-items-center font-medium text-sm gap-1 text-white">
                        <span class="material-symbols-rounded text-xl">visibility</span> 240
                    </div>
                </div>
                <p class="mb-5 text-sm text-white">The goal of the proposed ordinance is to create safe venues for
                    customers
                    and to discourage illegal activities. The governing officials of...</p>

                <button type="button"
                    class="px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none">
                    Learn More <span class="material-symbols-rounded">arrow_right_alt</span>
                </button>
            </div>
            <div class="news-box w-full flex flex-column">
                <div class="new-img flex align-items-center justify-content-center overflow-hidden">
                    <img src="/assets/layout/images/new-img-1.jpg" alt="" class="w-full h-full object-fit-cover">
                </div>
                <h3 class="text-white text-xl flex-1">Waste Industries Garbage Pick Up: Embeds</h3>
                <div class="mb-3 flex gap-3 align-items-center">
                    <div class="text flex align-items-center font-medium text-sm gap-1 text-white">
                        <span class="material-symbols-rounded text-xl">calendar_month</span> September 16, 2018
                    </div>
                    <div class="text flex align-items-center font-medium text-sm gap-1 text-white">
                        <span class="material-symbols-rounded text-xl">visibility</span> 240
                    </div>
                </div>
                <p class="mb-5 text-sm text-white">The goal of the proposed ordinance is to create safe venues for
                    customers
                    and to discourage illegal activities. The governing officials of...</p>

                <button type="button"
                    class="px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none">
                    Learn More <span class="material-symbols-rounded">arrow_right_alt</span>
                </button>
            </div>
        </div>
    </div>
</section>
<!--NEWS SEC-->

<!--NEWS LETTER SEC-->
<section class="news-letter-sec relative">
    <div class="news-letter-body relative max-w-1200 w-full mx-auto px-4">
        <div class="news-letter-box-list flex bg-white shadow-1">
            <div class="news-letter-box w-6 p-7 flex flex-column justify-content-center">
                <h2
                    class="line-2 relative mb-5 pb-5 inline-flex w-fit align-items-start text-6xl font-extrabold line-height-1">
                    Sign up for News & Alerts</h2>
                <p>Stay connected to the City of Riverside by signing up to receive official news and alerts by email!
                </p>
                <div class="news-letter-form mt-5 relative flex align-items-center">
                    <input type="text" pInputText class="h-3-3rem border-noround flex-1"
                        placeholder="Your email address" />
                    <button type="button"
                        class="px-6 p-element p-ripple border-noround p-button p-component h-3-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none">
                        Sign Up
                    </button>
                </div>
            </div>
            <div class="news-letter-img w-6 flex align-items-center justify-content-center overflow-hidden">
                <img src="/assets/layout/images/news-letter-img.jpg" alt="" class="w-full h-full object-fit-cover">
            </div>
        </div>
    </div>
</section>
<!--NEWS LETTER SEC-->

<!--WHAT HAPPNENING SEC-->
<section class="what-happning-sec mt-8 relative px-4">
    <div class="what-happning-body relative w-full mx-auto flex bg-orange-50">
        <div class="what-happning-img w-6 flex align-items-center justify-content-center overflow-hidden">
            <img src="/assets/layout/images/whats-happnening-mg.jpg" alt="" class="w-full h-full object-fit-cover">
        </div>
        <div class="what-happning-right w-6 p-8">
            <div class="what-happning-title">
                <h5 class="line-2 m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400">Join the fun in our city!
                </h5>
                <h2 class="m-0 mb-6 relative text-6xl line-height-2 font-bold text-color">What's Happening</h2>
            </div>
            <div class="what-happning-list">
                <div
                    class="what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border">
                    <div class="wh-img-box flex align-items-center justify-content-center overflow-hidden">
                        <img src="/assets/layout/images/arches-1.jpg" alt="" class="w-full h-full object-fit-cover">
                    </div>
                    <div class="wh-cnt-sec flex-1 flex align-items-center gap-6">
                        <div class="wh-cnt flex flex-column">
                            <h3 class="text-xl font-bold">Heritage 10th Anniversary</h3>
                            <div class="flex align-items-center gap-3 flex-wrap">
                                <div class="flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100">
                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>
                                </div>
                                <div class="flex align-items-center gap-2">All Day at <b>Baker Hall</b></div>
                            </div>
                        </div>
                        <button type="button"
                            class="px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold">
                            Find out more
                        </button>
                    </div>
                </div>
                <div
                    class="what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border">
                    <div class="wh-img-box flex align-items-center justify-content-center overflow-hidden">
                        <img src="/assets/layout/images/arches-2.jpg" alt="" class="w-full h-full object-fit-cover">
                    </div>
                    <div class="wh-cnt-sec flex-1 flex align-items-center gap-6">
                        <div class="wh-cnt flex flex-column">
                            <h3 class="text-xl font-bold">Along Pines Run</h3>
                            <div class="flex align-items-center gap-3 flex-wrap">
                                <div class="flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100">
                                    <span>July 22, 2027</span>
                                </div>
                                <div class="flex align-items-center gap-2">12:00 am at <b>Boulder City Council</b></div>
                            </div>
                        </div>
                        <button type="button"
                            class="px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold">
                            Find out more
                        </button>
                    </div>
                </div>
                <div class="what-happning-box flex align-items-center gap-5">
                    <div class="wh-img-box flex align-items-center justify-content-center overflow-hidden">
                        <img src="/assets/layout/images/arches-3.jpg" alt="" class="w-full h-full object-fit-cover">
                    </div>
                    <div class="wh-cnt-sec flex-1 flex align-items-center gap-6">
                        <div class="wh-cnt flex flex-column">
                            <h3 class="text-xl font-bold">Touch A Truck</h3>
                            <div class="flex align-items-center gap-3 flex-wrap">
                                <div class="flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100">
                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>
                                </div>
                                <div class="flex align-items-center gap-2">9:00 am - 5:00 pm at <b>Baker Hall</b></div>
                            </div>
                        </div>
                        <button type="button"
                            class="px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold">
                            Find out more
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--WHAT HAPPNENING SEC-->

<app-footer></app-footer>