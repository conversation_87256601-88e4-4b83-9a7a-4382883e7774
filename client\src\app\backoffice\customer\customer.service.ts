import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CustomerService {
  private apiUrl =
    'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com/api';
  private headers = new HttpHeaders({
    Authorization:
      'bearer 81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695', // Replace with your actual token
  });

  constructor(private http: HttpClient) {}

  getCustomers(
    page: number,
    pageSize: number,
    sortField?: string,
    sortOrder?: number,
    searchTerm?: string
  ): Observable<any[]> {
    let params = new HttpParams()
      .set('pagination[page]', page.toString())
      .set('pagination[pageSize]', pageSize.toString());
    if (sortField && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc
      params = params.set('sort', `${sortField}:${order}`);
    }
    if (searchTerm) {
      params = params
        .set('filters[$or][0][bp_id][$containsi]', searchTerm)
        .set('filters[$or][1][bp_full_name][$containsi]', searchTerm)
        .set('filters[$or][2][email][$containsi]', searchTerm)
        .set('filters[$or][3][phone][$containsi]', searchTerm);
    }
    return this.http.get<any[]>(`${this.apiUrl}/business-partners`, {
      headers: this.headers,
      params,
    });
  }
  getCustomerByID(custid: string) {
    return this.http.get<any[]>(
      `${this.apiUrl}/customers?filters[bp_id][$eq]=${custid}&populate=*`,
      { headers: this.headers }
    );
  }

  getCustomerByIDName(customerdata: string): Observable<any[]> {
    let params = new HttpParams();
    if (customerdata) {
      params = params
        .set('filters[$or][0][customer_id][$containsi]', customerdata)
        .set('filters[$or][1][customer_name][$containsi]', customerdata);
    }

    return this.http.get<any[]>(`${this.apiUrl}/customers`, {
      headers: this.headers,
      params,
    });
  }
}
