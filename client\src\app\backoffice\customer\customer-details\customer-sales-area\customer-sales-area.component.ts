import { Component, Input } from '@angular/core';

interface expandedRows {
  [key: string]: boolean;
}

@Component({
  selector: 'app-customer-sales-area',
  templateUrl: './customer-sales-area.component.html',
  styleUrl: './customer-sales-area.component.scss',
})
export class CustomerSalesAreaComponent {
  @Input() sales_area: any = null;
  public isExpanded: boolean = false;
  public expandedRows: expandedRows = {};
  
  expandAll() {
    if (!this.isExpanded) {
      this.sales_area.forEach((area: any) =>
        area?.id ? (this.expandedRows[area.id] = true) : ''
      );
    } else {
      this.expandedRows = {};
    }
    this.isExpanded = !this.isExpanded;
  }
}
