<div class="grid">
  <div class="col-12">
    <div class="card">
      <h5>Filter Menu</h5>
      <p-table
        #dt1
        [value]="customers"
        dataKey="id"
        [rows]="10"
        (onLazyLoad)="loadCustomers($event)"
        [loading]="loading"
        [rowHover]="true"
        styleClass="p-datatable-gridlines"
        [paginator]="true"
        [globalFilterFields]="[
          'bp_id',
          'bp_full_name',
          'bp_category',
          'email',
          'phone'
        ]"
        [totalRecords]="totalRecords"
        [lazy]="true"
        responsiveLayout="scroll"
      >
        <ng-template pTemplate="caption">
          <div class="flex justify-content-between flex-column sm:flex-row">
            <button
              pButton
              label="Clear"
              class="p-button-outlined mb-2"
              icon="pi pi-filter-slash"
              (click)="clear(dt1)"
            ></button>
            <span class="p-input-icon-left mb-2">
              <i class="pi pi-search"></i>
              <input
                pInputText
                type="text"
                #filter
                [(ngModel)]="globalSearchTerm"
                (input)="onGlobalFilter(dt1, $event)"
                placeholder="Search Keyword"
                class="w-full"
              />
            </span>
          </div>
        </ng-template>
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 12rem" pSortableColumn="bp_id">
              <div class="flex justify-content-between align-items-center">
                ID 
                <div class="flex align-items-center">
                  <p-sortIcon field="bp_id"></p-sortIcon>
                  <!-- <p-columnFilter
                    type="text"
                    field="bp_id"
                    display="menu"
                    placeholder="Search by ID"
                  ></p-columnFilter> -->
                </div>
              </div>
            </th>
            <th style="min-width: 12rem" pSortableColumn="bp_full_name">
              <div class="flex justify-content-between align-items-center">
                Name 
                <div class="flex align-items-center">
                  <p-sortIcon field="bp_full_name"></p-sortIcon>
                  <!-- <p-columnFilter
                    type="text"
                    field="bp_full_name"
                    display="menu"
                    placeholder="Search by Name"
                  ></p-columnFilter> -->
                </div>
              </div>
            </th>
            <th style="min-width: 12rem" pSortableColumn="bp_category">
              <div class="flex justify-content-between align-items-center">
                Category 
                <div class="flex align-items-center">
                  <p-sortIcon field="bp_category"></p-sortIcon>
                  <!-- <p-columnFilter
                    type="text"
                    field="bp_category"
                    display="menu"
                    placeholder="Search by Category"
                  ></p-columnFilter> -->
                </div>
              </div>
            </th>
            <th style="min-width: 10rem" pSortableColumn="email">
              <div class="flex justify-content-between align-items-center">
                Email 
                <div class="flex align-items-center">
                  <p-sortIcon field="email"></p-sortIcon>
                <!-- <p-columnFilter
                  type="email"
                  field="email"
                  display="menu"
                  placeholder="Search by Email"
                ></p-columnFilter> -->
                </div>
              </div>
            </th>
            <th style="min-width: 12rem" pSortableColumn="phone">
              <div class="flex justify-content-between align-items-center">
                Phone 
                <div class="flex align-items-center">
                  <p-sortIcon field="phone"></p-sortIcon>
                  <!-- <p-columnFilter
                    type="number"
                    field="phone"
                    display="menu"
                    placeholder="Search by Phone"
                  ></p-columnFilter> -->
                </div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-customer>
          <tr class="cursor-pointer" [routerLink]="'/backoffice/customer/'+customer.bp_id">
            <td>
              {{ customer.bp_id }}
            </td>
            <td>
              {{ customer.bp_full_name }}
            </td>
            <td>
              {{ customer.bp_category }}
            </td>
            <td>
              {{ customer.email }}
            </td>
            <td>
              {{ customer.phone }}
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="8">No customers found.</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="loadingbody">
          <tr>
            <td colspan="8">Loading customers data. Please wait.</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
