import { Component, Input } from '@angular/core';

interface expandedRows {
  [key: string]: boolean;
}

@Component({
  selector: 'app-customer-partner',
  templateUrl: './customer-partner.component.html',
  styleUrl: './customer-partner.component.scss',
})
export class CustomerPartnerComponent {
  @Input() partner_functions: any = null;
  public isExpanded: boolean = false;
  public expandedRows: expandedRows = {};

  expandAll() {
    if (!this.isExpanded) {
      this.partner_functions.forEach((partner: any) =>
        partner?.id ? (this.expandedRows[partner.id] = true) : ''
      );
    } else {
      this.expandedRows = {};
    }
    this.isExpanded = !this.isExpanded;
  }
}
