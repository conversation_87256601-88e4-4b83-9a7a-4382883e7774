import { Component, Input } from '@angular/core';

interface expandedRows {
  [key: string]: boolean;
}

@Component({
  selector: 'app-customer-sales-texts',
  templateUrl: './customer-sales-texts.component.html',
  styleUrl: './customer-sales-texts.component.scss'
})
export class CustomerSalesTextsComponent {
  @Input() sales_text: any = null;
  public isExpanded: boolean = false;
  public expandedRows: expandedRows = {};

  expandAll() {
    if (!this.isExpanded) {
      this.sales_text.forEach((text: any) =>
        text?.id ? (this.expandedRows[text.id] = true) : ''
      );
    } else {
      this.expandedRows = {};
    }
    this.isExpanded = !this.isExpanded;
  }
}
