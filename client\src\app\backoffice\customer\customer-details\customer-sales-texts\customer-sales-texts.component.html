<div class="col-12">
  <div class="card">
    <p-table
      [value]="sales_text"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="caption">
        <ng-container *ngIf="sales_text?.length > 0">
          <button
            pButton
            icon="pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}"
            label="{{ isExpanded ? 'Collapse All' : 'Expand All' }}"
            (click)="expandAll()"
          ></button>
          <div class="flex table-header"></div>
        </ng-container>
      </ng-template>
      <ng-template pTemplate="header">
        <tr *ngIf="sales_text?.length > 0">
          <th style="width: 3rem"></th>
          <th pSortableColumn="name">
            Text
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-sales_text let-expanded="expanded">
        <tr *ngIf="sales_text?.length > 0">
          <td>
            <button
              type="button"
              pButton
              pRipple
              [pRowToggler]="sales_text"
              class="p-button-text p-button-rounded p-button-plain"
              [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
            ></button>
          </td>
          <td>
            {{ sales_text?.description }}
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="rowexpansion" let-salea_area>
        <tr>
          <td style="width: 3rem"></td>
          <td colspan="2">
            <div class="grid mx-0">
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Text</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ sales_text?.description || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Text Id
                </span>
                <span class="block font-medium mb-3 text-600">
                  {{ sales_text?.long_text_id || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Language</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ sales_text?.language || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Created By</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ sales_text?.created_by || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Created At</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ sales_text?.created_at || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Updated At</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ sales_text?.updated_at || "-" }}
                </span>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6">There are no Texts Available for this record.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
