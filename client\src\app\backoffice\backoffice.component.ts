import { Component, Renderer2 } from '@angular/core';

@Component({
  selector: 'app-backoffice',
  templateUrl: './backoffice.component.html',
  styleUrl: './backoffice.component.scss',
})
export class BackofficeComponent {
  constructor(private renderer: Renderer2) {}

  ngOnInit(): void {
    // Inject theme 
    const href = 'assets/layout/styles/theme/theme-dark/magenta/theme.css';
    const link = this.renderer.createElement('link');
    this.renderer.setAttribute(link, 'id', 'theme-link');
    this.renderer.setAttribute(link, 'rel', 'stylesheet');
    this.renderer.setAttribute(link, 'type', 'text/css');
    this.renderer.setAttribute(link, 'href', href);

    // Append the link tag to the head of the document
    this.renderer.appendChild(document.head, link);
  }

  ngOnDestroy(): void {
    // Find and remove the link tag when the component is destroyed
    const link = document.getElementById('theme-link');
    if (link) {
      link.remove();
    }
  }
}
