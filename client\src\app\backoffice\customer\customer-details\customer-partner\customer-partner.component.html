<div class="col-12">
  <div class="card">
    <p-table
      [value]="partner_functions"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="caption">
        <ng-container *ngIf="partner_functions?.length > 0">
          <button
            pButton
            icon="pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}"
            label="{{ isExpanded ? 'Collapse All' : 'Expand All' }}"
            (click)="expandAll()"
          ></button>
          <div class="flex table-header"></div>
        </ng-container>
      </ng-template>
      <ng-template pTemplate="header">
        <tr *ngIf="partner_functions?.length > 0">
          <th style="width: 3rem"></th>
          <th pSortableColumn="name">Partner Name</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-partner let-expanded="expanded">
        <tr *ngIf="partner_functions?.length > 0">
          <td>
            <button
              type="button"
              pButton
              pRipple
              [pRowToggler]="partner"
              class="p-button-text p-button-rounded p-button-plain"
              [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
            ></button>
          </td>
          <td>
            {{ partner?.name }}
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="rowexpansion" let-partner>
        <tr>
          <td style="width: 3rem"></td>
          <td colspan="2">
            <div class="grid mx-0">
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Name</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ partner?.name || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Address</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ partner?.address || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Sales Org</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ partner?.sales_organization || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Distribution Channel</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ partner?.distribution_channel || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Division</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ partner?.division || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Partner Counter</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ partner?.partner_counter || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Partner Function
                </span>
                <span class="block font-medium mb-3 text-600">
                  {{ partner?.partner_function || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Customer No
                </span>
                <span class="block font-medium mb-3 text-600">
                  {{ partner?.bp_customer_number || "-" }}
                </span>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6">There are no Partner Available for this record.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
