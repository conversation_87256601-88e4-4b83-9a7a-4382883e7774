<div class="col-12">
  <div class="card">
    <p-table
      [value]="companies"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="caption">
        <ng-container *ngIf="companies?.length > 0">
          <button
            pButton
            icon="pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}"
            label="{{ isExpanded ? 'Collapse All' : 'Expand All' }}"
            (click)="expandAll()"
          ></button>
          <div class="flex table-header"></div>
        </ng-container>
      </ng-template>
      <ng-template pTemplate="header">
        <tr *ngIf="companies?.length > 0">
          <th style="width: 3rem"></th>
          <th pSortableColumn="name">
            Company Code 
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-company let-expanded="expanded">
        <tr *ngIf="companies?.length > 0">
          <td>
            <button
              type="button"
              pButton
              pRipple
              [pRowToggler]="company"
              class="p-button-text p-button-rounded p-button-plain"
              [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
            ></button>
          </td>
          <td>
            {{ company?.company_code }}
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="rowexpansion" let-company>
        <tr>
          <td style="width: 3rem"></td>
          <td colspan="2">
            <div class="grid mx-0">
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Company Code</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ company?.company_code || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Customer Code</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ company?.customer_id || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Deletion Indicator</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ company?.deletion_indicator || "-" }}
                </span>
              </div>
              <div class="col-12 lg:col-4">
                <span class="text-900 block font-medium mb-3 font-bold"
                  >Customer Account Group</span
                >
                <span class="block font-medium mb-3 text-600">
                  {{ company?.customer_account_group || "-" }}
                </span>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6">There are no companies Available for this record.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
