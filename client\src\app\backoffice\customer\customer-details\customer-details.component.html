  <div class="grid">
    <div class="field col-12 md:col-4">
      <h5
        *ngIf="
          customerDetails?.business_partner?.bp_id &&
          customerDetails?.business_partner?.bp_full_name
        "
      >
        {{ customerDetails?.business_partner?.bp_id }} -
        {{ customerDetails?.business_partner?.bp_full_name }}
      </h5>
    </div>

    <div class="field col-12 md:col-4">
      <p-autoComplete
        [(ngModel)]="selectedCustomer"
        [suggestions]="filteredCustomers"
        (completeMethod)="searchCustomers($event)"
        (onSelect)="onCustomerSelect($event)"
        field="searchword"
        [dropdown]="false"
        class="p-fluid"
        placeholder="Search Customer by ID or Name"
      />
    </div>
    <div class="field col-12 md:col-4">
       <p-button icon="pi pi-arrow-left" class="p-button-primary p-back-button" label="Back" (onClick)="goToBack()"></p-button>
    </div>
  </div>
<p-tabView>
  <p-tabPanel *ngFor="let tab of tabs; let i = index" [header]="tab.title">
    <ng-container [ngSwitch]="tab.value">
      <ng-container *ngSwitchCase="'COMPANIES'">
        <app-customer-companies
          [companies]="customerDetails?.companies"
        ></app-customer-companies>
      </ng-container>
      <ng-container *ngSwitchCase="'PARTNER_FUNCTION'">
        <app-customer-partner
          [partner_functions]="customerDetails?.partner_functions"
        ></app-customer-partner>
      </ng-container>
      <ng-container *ngSwitchCase="'SALES_AREA'">
        <app-customer-sales-area
          [sales_area]="customerDetails?.sales_areas"
        ></app-customer-sales-area>
      </ng-container>
      <ng-container *ngSwitchCase="'TEXTS'">
        <app-customer-sales-texts
          [sales_text]="customerDetails?.customer_texts"
        ></app-customer-sales-texts>
      </ng-container>
      <ng-container *ngSwitchDefault>
        <app-customer-info
          [customerDetails]="customerDetails"
        ></app-customer-info>
      </ng-container>
    </ng-container>
  </p-tabPanel>
</p-tabView>
