import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppLayoutComponent } from './layout/app.layout.component';
import { StoreComponent } from './store.component';
import { contentResolver } from '../core/content-resolver';

const routes: Routes = [
  {
    path: '',
    component: StoreComponent,
    children: [
      {
        path: '',
        component: AppLayoutComponent,
        children: [
          {
            path: '',
            loadChildren: () =>
              import('./home/<USER>').then((m) => m.HomeModule),
            resolve: {
              content: contentResolver,
            },
            data: {
              breadcrumb: 'Home',
              slug: 'home',
            },
          },
        ],
      },
      { path: '**', redirectTo: '/notfound' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class StoreRoutingModule { }
