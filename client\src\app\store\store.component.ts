import { Component, Renderer2 } from '@angular/core';
import { PrimeNGConfig } from 'primeng/api';
import { AppConfig, LayoutService } from './layout/service/app.layout.service';

@Component({
  selector: 'app-store',
  templateUrl: './store.component.html',
  styleUrl: './store.component.scss',
})
export class StoreComponent {
  constructor(
    private primengConfig: PrimeNGConfig,
    private renderer: Renderer2,
    private layoutService: LayoutService
  ) {}

  ngOnInit(): void {
    // Inject theme 
    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';
    const link = this.renderer.createElement('link');
    this.renderer.setAttribute(link, 'id', 'theme-link');
    this.renderer.setAttribute(link, 'rel', 'stylesheet');
    this.renderer.setAttribute(link, 'type', 'text/css');
    this.renderer.setAttribute(link, 'href', href);

    // Append the link tag to the head of the document
    this.renderer.appendChild(document.head, link);

    this.primengConfig.ripple = true; //enables core ripple functionality
    //optional configuration with the default configuration
    const config: AppConfig = {
      ripple: false, //toggles ripple on and off
      menuMode: 'reveal', //layout mode of the menu, valid values are "static", "overlay", "slim", "horizontal", "drawer" and "reveal"
      colorScheme: 'light', //color scheme of the template, valid values are "light" and "dark"
      theme: 'snjya', //default component theme for PrimeNG
      scale: 14, //size of the body font size to scale the whole application
    };
    this.layoutService.config.set(config);
  }

  ngOnDestroy(): void {
    // Find and remove the link tag when the component is destroyed
    const link = document.getElementById('theme-link');
    if (link) {
      link.remove();
    }
  }
}
