import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './home.component';
import { contentResolver } from '../core/content-resolver';

const routes: Routes = [
  {
    path: '', component: HomeComponent, resolve: {
      content: contentResolver,
    },
    data: {
      slug: 'home',
    },
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HomeRoutingModule { }
