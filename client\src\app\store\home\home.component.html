<div class="col-12 all-overview-body m-0 p-0 border-round-lg surface-card">

    <p-galleria [value]="images" [circular]="true" [showItemNavigators]="true" [showThumbnails]="false"
        [autoPlay]="true" [transitionInterval]="transitionInterval">
        <ng-template pTemplate="item" let-item>
            <div
                class="overview-banner relative flex align-items-center justify-content-center h-32rem w-full border-round-lg overflow-hidden">
                <img [src]="item.url" [alt]="item.alt" class="max-w-full w-full h-full" />
                <div
                    class="banner-overlay absolute flex align-items-end justify-content-center w-full h-full top-0 left-0 p-8">
                    <h1
                        class="m-0 p-0 relative text-4xl font-semibold text-white text-center max-w-1200 text-shadow-l-black">
                        {{ content?.i18n?.['label.title'] || '' }}
                    </h1>
                </div>
            </div>
        </ng-template>
    </p-galleria>

    <div [routerLink]="['/store/dashboard']"
        class="home-box-list relative flex justify-content-center mt-8 w-full gap-4 mx-auto flex-wrap">
        <div
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/dashboard.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Dashboard</h5>
        </div>
        <div [routerLink]="['/store/prospects']"
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/prospect.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Prospects</h5>
        </div>
        <div [routerLink]="['/store/account']"
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/account.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Account</h5>
        </div>
        <div [routerLink]="['/store/contacts']"
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/contact.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Contacts</h5>
        </div>
        <div [routerLink]="['/store/activities/calls']"
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/activities.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Activities</h5>
        </div>
        <div [routerLink]="['/store/opportunities']"
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/opportunities.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Opportunities</h5>
        </div>
        <div [routerLink]="['/store/ai-insights']"
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/analytics.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Ai Insights</h5>
        </div>
        <div [routerLink]="['/store/sales-quotes']"
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/quotes.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Sales Quotes</h5>
        </div>
        <div [routerLink]="['/store/sales-orders']"
            class="home-box flex flex-column align-items-center justify-content-center surface-25 h-8rem w-10rem border-round cursor-pointer">
            <img src="assets/layout/images/orders.svg" alt="" />
            <h5 class="m-0 mt-3 relative block text-center text-md text-color">Sales Orders</h5>
        </div>
    </div>
</div>