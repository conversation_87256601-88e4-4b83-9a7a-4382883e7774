::ng-deep {

    .bg-light-blue {
        background: #c3dbff !important;
    }

    .surface-25 {
        background-color: #e1e2e885 !important;
    }

    .object-fit-contain {
        object-fit: contain;
    }

    .object-fit-cover {
        object-fit: cover;
    }

    .transition-03 {
        transition: all 0.3s ease-in-out;
    }

    .h-36rem {
        height: 36rem !important;
    }

    .h-34rem {
        height: 34rem !important;
    }

    .surface-b {
        background: var(--surface-b) !important;
    }

    .h-3-3rem {
        height: 3.3rem;
    }

    .d-grid {
        display: grid !important;
    }

    .w-fit {
        width: fit-content;
    }

    .min-w-3rem {
        min-width: 3rem !important;
    }

    .min-w-16 {
        min-width: 16rem !important;
    }

    .min-w-14 {
        min-width: 14rem !important;
    }

    .min-h-14 {
        min-height: 14rem !important;
    }

    .min-h-18 {
        min-height: 18rem !important;
    }

    .min-h-30 {
        min-height: 30rem !important;
    }

    .font-900 {
        font-weight: 900 !important;
    }

    .surface-b {
        background: var(--surface-b) !important;
    }

    .object-fit-contain {
        object-fit: contain;
    }

    .transition-03 {
        transition: all 0.3s ease-in-out;
    }

    .p-datatable-wrapper {
        thead {
            p-sorticon {
                svg {
                    color: var(--white);
                }
            }
        }
    }

    .header-title,
    .left-border {
        &:before {
            position: absolute;
            content: "";
            left: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            width: 5px;
            height: 24px;
            background: var(--primary-color);
            border-radius: 50px;
        }
    }

    .layout-sidebar {
        width: 20rem;
    }

    .layout-content-wrapper {
        background: var(--surface-0);
    }

    .bg-whight-light {
        background: #f6f7f9;
    }

    .all-overview-body {
        min-height: calc(100vh - 90px);

        .p-galleria .p-galleria-item-nav {
            color: var(--text-color) !important;
            background: var(--surface-0) !important;
            width: 3rem !important;
            height: 3rem !important;
            transform: translateY(-50%);
            z-index: 99;

            .p-icon-wrapper {
                .p-icon {
                    width: 1.5rem;
                    height: 1.5rem;
                }
            }
        }

        .card-list {
            .v-details-list {
                .v-details-box {
                    .text {
                        min-width: 120px;
                    }
                }
            }
        }

        p-table {
            table {
                thead {
                    th {
                        height: 44px;
                        background: var(--primary-color);
                        color: var(--surface-0);
                    }
                }

                tbody {
                    td {
                        height: 44px;
                    }
                }
            }
        }

        .v-details-list {
            .v-details-box {
                .text {
                    min-width: 182px;
                    min-width: 182px;
                }
            }
        }

        .order-details-list {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }

        .order-details-list {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
    }

    .p-inputtext {
        appearance: auto !important;
    }

    .border-left-5 {
        border-left: 5px solid var(--orange-200);
    }

    .p-calendar {
        display: flex;

        .p-button-icon-only {
            width: 3rem;
        }
    }

    .max-w-1200 {
        max-width: 1200px;
    }

    .text-shadow-l-blue {
        text-shadow: 0 2px 6px rgba(0, 63, 147, 0.8);
    }

    .h-32rem {
        height: 32rem !important;
    }

    .h-2-8rem {
        height: 2.8rem !important;
    }

    .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {
        color: var(--text-color);
        font-weight: 600;
    }

    p-paginator {
        .p-paginator {
            padding: 20px 0;
            margin: 1.5rem 0 0 0;
            border-top: 1px solid var(--surface-d);
            border-radius: 0;

            button {
                width: 3rem;
                height: 2rem;
                border-radius: 0.3rem;
                border: 1px solid var(--surface-c);
            }

            p-dropdown {
                display: none;
            }
        }
    }


    /*---TABLE SEC---*/

    .table-sec tbody:before {
        line-height: 20px;
        content: "_";
        color: transparent;
        display: block;
    }

    .table-sec tbody tr:nth-child(odd) td {
        background: var(--surface-b);
    }

    .table-sec {
        thead {
            th {
                .p-checkbox {
                    .p-checkbox-box.p-highlight {
                        border-color: var(--surface-0);
                    }
                }

                &:last-child {
                    border-top-right-radius: 0.5rem !important;
                    border-bottom-right-radius: 0.5rem !important;
                }
            }
        }

        tbody {
            td {
                &:last-child {
                    border-top-right-radius: 0.5rem !important;
                    border-bottom-right-radius: 0.5rem !important;
                }
            }
        }
    }

    .p-datatable-scrollable>.p-datatable-wrapper {
        padding-bottom: 12px;
    }

    /*---TABLE SEC---*/

    /*---Details Page Tabs SEC---*/
    .details-tabs-list {
        .p-tabview-nav-content {
            .p-tabview-nav {
                padding: 0 16px;
                border-color: var(--surface-100);

                li {
                    position: relative;

                    &::before {
                        content: '';
                        position: absolute;
                        right: -1px;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                        background: var(--surface-50);
                        width: 1px;
                        height: 20px;
                    }

                    .p-tabview-nav-link {
                        border: none;
                        padding: 0;

                        .tab-link {
                            padding: 8px 14px;
                            min-height: 40px;
                            color: var(--gray-600);
                            gap: 0 6px;
                            border-radius: 10px 10px 0 0;
                            cursor: pointer;
                            font-weight: 500;

                            &:hover {
                                color: var(--primary-color);
                            }

                            &.active-tab {
                                background: #f6f7f9;
                                border: 2px solid var(--surface-100);
                                border-bottom: none;
                                font-weight: 600;
                                color: var(--gray-800);
                            }
                        }
                    }
                }

                .p-tabview-ink-bar {
                    display: none !important;
                }
            }
        }

        .p-tabview-panels {
            display: none;
        }

        .p-tabview-nav-btn.p-link {
            background: var(--surface-0);
        }
    }

    .details-tabs-result {
        min-height: calc(100vh - 192px);
    }

    .layout-sidebar .layout-menu li:nth-child(2) .layout-menuitem-root-text {
        margin: 12px 0;
        padding: 12px 0;
        border-top: 1px solid var(--surface-c);
        font-size: 14px;
        font-weight: 600;
    }

    /*---Details Page Tabs SEC---*/

    .card-heading {
        h4 {
            margin-left: 10px !important;
            min-height: 30px;
            align-items: center;
        }
    }

    .sidebar-hide {
        display: none;
    }

    .arrow-btn {
        top: 29px;
        left: 0;
        z-index: 99;
    }

    .arrow-round {
        transform: rotate(180deg);
    }

    .layout-sidebar .sidebar-header .app-logo .arrow-icon {
        transform: rotateY(180deg);
        position: absolute;
        right: 11px;
        top: 14px;
        color: var(--primary-color);
    }

    .layout-container.layout-reveal.layout-sidebar-active .layout-sidebar .sidebar-header .app-logo .arrow-icon {
        display: none !important;
    }

    .p-dropdown-label {
        display: flex;
        align-items: center;
    }

    .filter-sec {
        p-dropdown {

            .p-dropdown-label,
            .p-dropdown-trigger {
                color: var(--primary-700);
            }
        }

        .table-multiselect-dropdown {

            .p-overlay.p-component {
                left: -140px !important;
            }

            .p-multiselect-items-wrapper {
                p-multiselectitem {
                    li {
                        margin: 0 0 2px 0 !important;
                        min-width: 164px;
                    }
                }
            }

            .p-multiselect {
                .p-multiselect-label-container {
                    display: none;
                }

                .p-multiselect-label {
                    color: var(--primary-700);
                    align-items: center;
                    display: flex;
                    text-transform: capitalize;
                }
            }

            .p-multiselect-trigger {
                position: relative;
                width: 1.5rem;
                height: 1.5rem;

                chevrondownicon {
                    display: none;
                }

                &::before {
                    position: absolute;
                    content: "\e5d4";
                    width: 1.5rem;
                    height: 1.5rem;
                    font-family: "Material Symbols Rounded";
                    font-size: 1.5rem;
                    color: var(--primary-700);
                    line-height: 22px;
                }
            }
        }
    }

    .scrollable-table {
        table {
            thead {
                th {
                    min-width: 150px;
                    white-space: nowrap;
                }

                th.table-checkbox {
                    min-width: 32px;
                }
            }

            tbody {
                tr:nth-child(odd) {
                    td:first-child {
                        background: #f2f2f5;
                    }

                    td:nth-child(2) {
                        background: #f2f2f5;
                    }
                }

                td {
                    white-space: nowrap;
                }
            }
        }
    }

    .all-page-details {
        width: calc(100% - 28rem);
    }

    /*-----DARK THEME-----*/
    .layout-dark {
        .bg-whight-light {
            background: var(--surface-0);
        }

        .details-tabs-list {
            .p-tabview-nav-content {
                .p-tabview-nav {
                    li {
                        .p-tabview-nav-link {

                            .tab-link {

                                &.active-tab {
                                    background: var(--surface-0);
                                    color: var(--text-color);
                                }
                            }
                        }
                    }
                }
            }
        }

        .scrollable-table {
            table {
                tbody {
                    tr:nth-child(odd) {
                        td:first-child {
                            background: var(--surface-b);
                        }

                        td:nth-child(2) {
                            background: var(--surface-b);
                        }
                    }
                }
            }
        }

        .table-sec tbody tr:nth-child(odd) td {
            background: rgb(255 255 255 / 5%);
        }

    }

    .p-sortable-column-badge {
        display: none !important;
    }

    // .note-text {
    //     max-width: 520px;
    //     overflow: hidden;
    //     text-overflow: ellipsis;
    //     width: 100%;
    // }

    .note-text {
        max-width: 320px;
        width: 320px;
        white-space: normal !important;
    }

    .multiselect-dropdown {
        padding: 0;
        height: 3rem;

        .ng-select-container {
            background: none !important;
            border: none;
            height: 3rem !important;
            align-items: center;

            .ng-value-container {
                height: 2rem;

                .ng-input {
                    top: 0 !important;
                    bottom: 0;
                    margin: auto;
                    display: flex;
                    align-items: center;
                }
            }
        }
    }

    .ng-dropdown-panel {
        .ng-dropdown-panel-items {
            .ng-option {
                input {
                    min-width: 20px;
                }
            }
        }
    }

    .confirm-popup {
        .p-dialog {
            margin-right: 50px;

            .p-dialog-header {
                background: var(--surface-0);
                border-bottom: 1px solid var(--surface-100);

                h4 {
                    margin: 0;
                }
            }

            .p-dialog-content {
                background: var(--surface-0);
                padding: 1.714rem;
            }

            .p-dialog-footer {
                background: var(--surface-0);
                padding: 1.714rem;

                .p-button {
                    height: 2.5rem !important;
                    width: 8rem !important;
                    gap: 0.25rem !important;
                    font-weight: 500 !important;
                    border: none;
                    border-radius: 2rem;
                    justify-content: center;

                    &.p-confirm-dialog-reject {
                        color: var(--red-500) !important;
                        background-color: var(--red-100) !important;
                    }

                    &.p-confirm-dialog-accept {
                        color: var(--primary-700) !important;
                        background-color: #c3dbff !important;
                    }

                    .p-button-label {
                        flex: none !important;
                    }
                }
            }
        }
    }

}