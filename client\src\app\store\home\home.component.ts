import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ContentService } from 'src/app/core/services/content-vendor.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent {

  content!: any;
  images: any[] = [];
  transitionInterval = 2000;

  constructor(
    private route: ActivatedRoute,
    private CMSservice: ContentService
  ) { }

  ngOnInit() {
    this.content = this.route.snapshot.data['content'];
    const mediaComponent = this.CMSservice.getDataByComponentName(this.content.body, "crm.media");
    if (mediaComponent?.length) {
      this.images = mediaComponent[0].Images;
      this.transitionInterval = mediaComponent[0].timer || 2000;
    }
  }

}
