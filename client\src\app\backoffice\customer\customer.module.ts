import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CustomerComponent } from './customer.component';
import { CustomerRoutingModule } from './customer-routing.module';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { RippleModule } from 'primeng/ripple';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { ToastModule } from 'primeng/toast';
import { SliderModule } from 'primeng/slider';
import { RatingModule } from 'primeng/rating';
import { HttpClientModule } from '@angular/common/http';
import { CustomerDetailsComponent } from './customer-details/customer-details.component';
import { InputNumberModule } from 'primeng/inputnumber';
import { TabViewModule } from 'primeng/tabview';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { MessageService, ConfirmationService } from 'primeng/api';
import { CustomerInfoComponent } from './customer-details/customer-info/customer-info.component';
import { CustomerCompaniesComponent } from './customer-details/customer-companies/customer-companies.component';
import { CustomerPartnerComponent } from './customer-details/customer-partner/customer-partner.component';
import { CustomerSalesAreaComponent } from './customer-details/customer-sales-area/customer-sales-area.component';
import { CustomerSalesTextsComponent } from './customer-details/customer-sales-texts/customer-sales-texts.component';

@NgModule({
  declarations: [
    CustomerComponent,
    CustomerDetailsComponent,
    CustomerInfoComponent,
    CustomerCompaniesComponent,
    CustomerPartnerComponent,
    CustomerSalesAreaComponent,
    CustomerSalesTextsComponent
  ],
  imports: [
    CommonModule,
    CustomerRoutingModule,
    FormsModule,
    TableModule,
    RatingModule,
    ButtonModule,
    SliderModule,
    InputTextModule,
    ToggleButtonModule,
    RippleModule,
    MultiSelectModule,
    DropdownModule,
    ProgressBarModule,
    ToastModule,
    HttpClientModule,
    InputNumberModule,
    TabViewModule,
    AutoCompleteModule,
  ],
  providers: [MessageService, ConfirmationService],
})
export class CustomerModule {}
