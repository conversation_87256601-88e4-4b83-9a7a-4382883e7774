import { Component, Input } from '@angular/core';

interface expandedRows {
  [key: string]: boolean;
}

@Component({
  selector: 'app-customer-companies',
  templateUrl: './customer-companies.component.html',
  styleUrl: './customer-companies.component.scss',
})
export class CustomerCompaniesComponent {
  @Input() companies: any = null;
  public isExpanded: boolean = false;
  public expandedRows: expandedRows = {};

  expandAll() {
    if (!this.isExpanded) {
      this.companies.forEach((company: any) =>
        company?.id ? (this.expandedRows[company.id] = true) : ''
      );
    } else {
      this.expandedRows = {};
    }
    this.isExpanded = !this.isExpanded;
  }
}
