import { OnInit } from '@angular/core';
import { Component } from '@angular/core';

@Component({
  selector: 'app-menu',
  templateUrl: './app.menu.component.html',
})
export class AppMenuComponent implements OnInit {
  model: any[] = [];

  ngOnInit() {
    this.model = [
      {
        label: 'Apps',
        icon: 'pi pi-th-large',
        items: [
          {
            label: 'Dashboard',
            icon: 'pi pi-fw pi-home',
            routerLink: ['/backoffice'],
          },
          {
            label: 'Customer',
            icon: 'pi pi-fw pi-users',
            routerLink: ['/backoffice/customer'],
          },
        ],
      },
    ];
  }
}
