import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BackofficeComponent } from './backoffice.component';
import { AppLayoutComponent } from './layout/app.layout.component';

const routes: Routes = [
  {
    path: '',
    component: BackofficeComponent,
    children: [
      {
        path: '',
        component: AppLayoutComponent,
        children: [
          {
            path: '',
            data: { breadcrumb: 'Dashboard' },
            loadChildren: () =>
              import('./dashboard/dashboard.module').then(
                (m) => m.DashboardModule
              ),
          },
          {
            path: 'customer',
            data: { breadcrumb: 'Customer' },
            loadChildren: () =>
              import('./customer/customer.module').then(
                (m) => m.CustomerModule
              ),
          },
        ],
      },
      { path: '**', redirectTo: '/notfound' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BackofficeRoutingModule {}
