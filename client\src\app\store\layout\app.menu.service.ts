import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { MenuChangeEvent } from './api/menuchangeevent';

@Injectable({
    providedIn: 'root'
})
export class MenuService {

    private menuSource = new Subject<MenuChangeEvent>();
    private resetSource = new Subject();

    menuSource$ = this.menuSource.asObservable();
    resetSource$ = this.resetSource.asObservable();

    onMenuStateChange(event: MenuChangeEvent) {
        this.menuSource.next(event);
    }

    reset() {
        this.resetSource.next(true);
    }

    private activeMenuSubject = new BehaviorSubject<string>('');
    activeMenu$ = this.activeMenuSubject.asObservable();

    setActiveMenu(menuName: string): void {
        this.activeMenuSubject.next(menuName);
    }

    getActiveMenu(): string {
        return this.activeMenuSubject.value;
    }
}
