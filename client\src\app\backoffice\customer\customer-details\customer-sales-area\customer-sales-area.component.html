<div class="col-12">
    <div class="card">
      <p-table
        [value]="sales_area"
        dataKey="id"
        [expandedRowKeys]="expandedRows"
        responsiveLayout="scroll"
      >
        <ng-template pTemplate="caption">
          <ng-container *ngIf="sales_area?.length > 0">
            <button
            pButton
            icon="pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}"
            label="{{ isExpanded ? 'Collapse All' : 'Expand All' }}"
            (click)="expandAll()"
          ></button>
          <div class="flex table-header"></div>
          </ng-container>
        </ng-template>
        <ng-template pTemplate="header">
          <tr *ngIf="sales_area?.length > 0">
            <th style="width: 3rem"></th>
            <th pSortableColumn="name">
             Organization
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-sale_area let-expanded="expanded">
          <tr *ngIf="sales_area?.length > 0">
            <td>
              <button
                type="button"
                pButton
                pRipple
                [pRowToggler]="sale_area"
                class="p-button-text p-button-rounded p-button-plain"
                [icon]="
                  expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'
                "
              ></button>
            </td>
            <td>
              {{ sale_area?.sales_organization }}
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="rowexpansion" let-sale_area>
          <tr>
            <td style="width: 3rem"></td>
            <td colspan="2">
              <div class="grid mx-0">
                <div class="col-12 lg:col-4">
                  <span class="text-900 block font-medium mb-3 font-bold"
                    >Organization</span
                  >
                  <span class="block font-medium mb-3 text-600">
                    {{ sale_area?.sales_organization || "-" }}
                  </span>
                </div>
                <div class="col-12 lg:col-4">
                  <span class="text-900 block font-medium mb-3 font-bold"
                    >Distribution Channel</span
                  >
                  <span class="block font-medium mb-3 text-600">
                    {{ sale_area?.distribution_channel || "-" }}
                  </span>
                </div>
                <div class="col-12 lg:col-4">
                  <span class="text-900 block font-medium mb-3 font-bold"
                    >Division</span
                  >
                  <span class="block font-medium mb-3 text-600">
                    {{ sale_area?.division || "-" }}
                  </span>
                </div>
                <div class="col-12 lg:col-4">
                  <span class="text-900 block font-medium mb-3 font-bold"
                    >Currency</span
                  >
                  <span class="block font-medium mb-3 text-600">
                    {{ sale_area?.currency || "-" }}
                  </span>
                </div>
                <div class="col-12 lg:col-4">
                  <span class="text-900 block font-medium mb-3 font-bold"
                    >Account Assignment Group</span
                  >
                  <span class="block font-medium mb-3 text-600">
                    {{ sale_area?.customer_account_assignment_group || "-" }}
                  </span>
                </div>
                <div class="col-12 lg:col-4">
                  <span class="text-900 block font-medium mb-3 font-bold"
                    >Customer Group</span
                  >
                  <span class="block font-medium mb-3 text-600">
                    {{ sale_area?.customer_group || "-" }}
                  </span>
                </div>
                <div class="col-12 lg:col-4">
                  <span class="text-900 block font-medium mb-3 font-bold"
                    >Customer Price Group
                  </span>
                  <span class="block font-medium mb-3 text-600">
                    {{ sale_area?.customer_price_group || "-" }}
                  </span>
                </div>
                <div class="col-12 lg:col-4">
                  <span class="text-900 block font-medium mb-3 font-bold"
                    >Customer Pricing Procedure
                  </span>
                  <span class="block font-medium mb-3 text-600">
                    {{ sale_area?.customer_pricing_procedure || "-" }}
                  </span>
                </div>
                <div class="col-12 lg:col-4">
                    <span class="text-900 block font-medium mb-3 font-bold"
                      >Delivery Blocked For Customer
                    </span>
                    <span class="block font-medium mb-3 text-600">
                      {{ sale_area?.delivery_is_blocked_for_customer || "-" }}
                    </span>
                  </div>
                <div class="col-12 lg:col-4">
                    <span class="text-900 block font-medium mb-3 font-bold"
                      >Deletion Indicator
                    </span>
                    <span class="block font-medium mb-3 text-600">
                      {{ sale_area?.deletion_indicator || "-" }}
                    </span>
                  </div>
                  <div class="col-12 lg:col-4">
                    <span class="text-900 block font-medium mb-3 font-bold"
                      >Sales Group
                    </span>
                    <span class="block font-medium mb-3 text-600">
                      {{ sale_area?.sales_group || "-" }}
                    </span>
                  </div>
                  <div class="col-12 lg:col-4">
                    <span class="text-900 block font-medium mb-3 font-bold"
                      >Sales Office
                    </span>
                    <span class="block font-medium mb-3 text-600">
                      {{ sale_area?.sales_office || "-" }}
                    </span>
                  </div>
                  <div class="col-12 lg:col-4">
                    <span class="text-900 block font-medium mb-3 font-bold"
                      >Shipping Condition
                    </span>
                    <span class="block font-medium mb-3 text-600">
                      {{ sale_area?.shipping_condition || "-" }}
                    </span>
                  </div>
                  <div class="col-12 lg:col-4">
                    <span class="text-900 block font-medium mb-3 font-bold"
                      >Supplying Plant
                    </span>
                    <span class="block font-medium mb-3 text-600">
                      {{ sale_area?.supplying_plant || "-" }}
                    </span>
                  </div>
                  <div class="col-12 lg:col-4">
                    <span class="text-900 block font-medium mb-3 font-bold"
                      >Account Group
                    </span>
                    <span class="block font-medium mb-3 text-600">
                      {{ sale_area?.customer_account_group || "-" }}
                    </span>
                  </div>
              </div>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="6">There are no Sales Area Available for this record.</td>
          </tr>
      </ng-template>
      </p-table>
    </div>
  </div>