import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Table } from 'primeng/table';
import { MessageService, ConfirmationService } from 'primeng/api';
import { CustomerService } from './customer.service';

@Component({
  selector: 'app-customer',
  templateUrl: './customer.component.html',
  styleUrl: './customer.component.scss',
  providers: [MessageService, ConfirmationService],
})
export class CustomerComponent implements OnInit {
  public customers: any[] = [];
  public totalRecords: number = 0;
  public loading: boolean = true;
  public globalSearchTerm: string = '';
  @ViewChild('filter') filter!: ElementRef;

  constructor(private customerService: CustomerService) {}

  ngOnInit() {
    this.loadCustomers({ first: 0, rows: 10 });
  }

  loadCustomers(event: any) {
    this.loading = true;
    const page = event.first / event.rows + 1;
    const pageSize = event.rows;
    const sortField = event.sortField;
    const sortOrder = event.sortOrder;

    this.customerService
      .getCustomers(page, pageSize, sortField, sortOrder, this.globalSearchTerm)
      .subscribe({
        next: (response: any) => {
          this.customers = response?.data || [];
          this.totalRecords = response?.meta?.pagination.total;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error fetching customers', error);
          this.loading = false;
        },
      });
  }

  onGlobalFilter(table: Table, event: Event) {
    this.loadCustomers({ first: 0, rows: 10 });
  }

  clear(table: Table) {
    this.globalSearchTerm = '';
    this.filter.nativeElement.value = '';
    this.loadCustomers({ first: 0, rows: 10 });
  }
}
