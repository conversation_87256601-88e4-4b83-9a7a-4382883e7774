import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { LayoutService } from './service/app.layout.service';
import { ActivatedRoute } from '@angular/router';
import { ContentService } from 'src/app/core/services/content-vendor.service';

@Component({
    selector: 'app-sidebar',
    templateUrl: './app.sidebar.component.html'
})
export class AppSidebarComponent implements OnInit {
    timeout: any = null;
    public logo = '';

    @ViewChild('menuContainer') menuContainer!: ElementRef;
    constructor(
        public layoutService: LayoutService,
        public el: ElementRef,
        private route: ActivatedRoute,
        private CMSservice: ContentService
    ) { }

    ngOnInit(): void {
        const commonContent = this.route.snapshot.data['commonContent'];
        if (commonContent?.body) {
            const logoComponent = this.CMSservice.getDataByComponentName(commonContent.body, "crm.logo");
            if (logoComponent?.length) {
                this.logo = logoComponent[0].Logo?.url || '';
            }
        }
    }

    onMouseEnter() {
        if (!this.layoutService.state.anchored) {
            if (this.timeout) {
                clearTimeout(this.timeout);
                this.timeout = null;
            }
            this.layoutService.state.sidebarActive = true;


        }
    }

    onMouseLeave() {
        if (!this.layoutService.state.anchored) {
            if (!this.timeout) {
                this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);
            }
        }
    }

    anchor() {
        this.layoutService.state.anchored = !this.layoutService.state.anchored;
    }

}
